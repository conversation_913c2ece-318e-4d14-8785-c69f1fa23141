version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: geoportal-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  app:
    build: .
    container_name: geoportal-server
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    volumes:
      - ./data/final:/app/data/final
      - ./logs:/app/logs
      - upload_temp:/app/tmp
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  worker:
    build: .
    container_name: geoportal-worker
    command: npm run worker
    environment:
      - NODE_ENV=production
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    volumes:
      - ./data/final:/app/data/final
      - ./logs:/app/logs
      - upload_temp:/app/tmp
    depends_on:
      redis:
        condition: service_healthy
      app:
        condition: service_healthy

volumes:
  redis_data:
  upload_temp:
