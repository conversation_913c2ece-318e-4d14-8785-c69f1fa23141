const express = require('express');
const {
  initUpload,
  uploadChunk,
  getUploadStatus
} = require('../controllers/uploadController');

const router = express.Router();

/**
 * @route   POST /api/upload/init-upload
 * @desc    Khởi tạo phiên upload mới
 * @access  Public
 * @body    { filename, totalChunks, totalSize, userId? }
 */
router.post('/init-upload', initUpload);

/**
 * @route   POST /api/upload/upload-chunk
 * @desc    Upload một chunk
 * @access  Public
 * @body    { uploadId, chunkIndex } + file chunk
 */
router.post('/upload-chunk', uploadChunk);

/**
 * @route   GET /api/upload/:uploadId/status
 * @desc    Lấy trạng thái upload
 * @access  Public
 */
router.get('/:uploadId/status', getUploadStatus);

module.exports = router;
