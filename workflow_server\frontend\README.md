# GeoPortal Frontend Components

Vue 3 Composition API components for GeoPortal File Upload System with TypeScript support and Tailwind CSS styling.

## 📁 Files Structure

```
frontend/
├── FileUploadWorkflow.vue    # Main upload component
├── composables/
│   └── useFileUpload.ts      # Upload logic composable
├── types.ts                  # TypeScript definitions
├── demo.html                 # Standalone demo
└── README.md                 # This file
```

## 🚀 Quick Start

### Option 1: Standalone Demo
```bash
# Serve the demo.html file
cd frontend
python -m http.server 8080
# Or use any static file server

# Open browser
http://localhost:8080/demo.html
```

### Option 2: Vue 3 Project Integration

#### 1. Install Dependencies
```bash
npm install vue@3 @types/node
npm install -D tailwindcss postcss autoprefixer typescript
```

#### 2. Copy Files
```bash
# Copy component files to your Vue project
cp FileUploadWorkflow.vue src/components/
cp composables/useFileUpload.ts src/composables/
cp types.ts src/types/
```

#### 3. Use in Your App
```vue
<template>
  <div>
    <FileUploadWorkflow 
      :api-base-url="apiUrl"
      @upload-started="onUploadStarted"
      @upload-completed="onUploadCompleted"
      @upload-failed="onUploadFailed"
    />
  </div>
</template>

<script setup lang="ts">
import FileUploadWorkflow from '@/components/FileUploadWorkflow.vue'

const apiUrl = 'http://your-server:3000/api'

const onUploadStarted = (file: File) => {
  console.log('Upload started:', file.name)
}

const onUploadCompleted = (result: any) => {
  console.log('Upload completed:', result)
}

const onUploadFailed = (error: string) => {
  console.error('Upload failed:', error)
}
</script>
```

## 🎨 Component Features

### FileUploadWorkflow.vue
- **File Selection**: Drag & drop or click to select
- **Chunk Upload**: Automatic file chunking for large files
- **Progress Tracking**: Real-time upload progress
- **Job Monitoring**: Background job status tracking
- **Error Handling**: User-friendly error messages
- **Responsive Design**: Mobile-friendly with Tailwind CSS

### useFileUpload.ts Composable
- **State Management**: Reactive upload state
- **API Integration**: RESTful API calls
- **File Processing**: Chunk upload logic
- **Job Polling**: Real-time job status updates
- **Error Handling**: Comprehensive error management

## 📋 Props & Events

### FileUploadWorkflow Props
```typescript
interface Props {
  apiBaseUrl?: string          // API base URL (default: 'http://localhost:3000/api')
  chunkSize?: number          // Chunk size in bytes (default: 10MB)
  maxFileSize?: number        // Max file size in bytes (default: 100GB)
  allowedExtensions?: string[] // Allowed file extensions
  userId?: string             // User ID for uploads
  autoStartPolling?: boolean  // Auto start job polling
  pollInterval?: number       // Polling interval in ms
}
```

### Events
```typescript
interface Emits {
  'upload-started': [file: File]
  'upload-progress': [progress: UploadProgress]
  'upload-completed': [result: any]
  'upload-failed': [error: string]
  'job-status-changed': [job: Job]
  'job-completed': [job: Job]
  'job-failed': [job: Job]
}
```

## 🔧 Configuration

### API Endpoints
The component expects these API endpoints:

- `POST /api/upload/init-upload` - Initialize upload
- `POST /api/upload/upload-chunk` - Upload file chunk
- `GET /api/upload/:id/status` - Get upload status
- `GET /api/jobs` - List jobs
- `GET /api/jobs/:id/status` - Get job status
- `POST /api/jobs/:id/cancel` - Cancel job
- `POST /api/jobs/:id/retry` - Retry job
- `GET /api/jobs/stats` - Get job statistics

### Environment Variables
```env
# API Configuration
VITE_API_BASE_URL=http://localhost:3000/api
VITE_CHUNK_SIZE=10485760
VITE_MAX_FILE_SIZE=107374182400
VITE_POLL_INTERVAL=2000
```

## 🎯 Usage Examples

### Basic Usage
```vue
<template>
  <FileUploadWorkflow />
</template>
```

### Advanced Usage
```vue
<template>
  <FileUploadWorkflow 
    :api-base-url="config.apiUrl"
    :chunk-size="config.chunkSize"
    :max-file-size="config.maxFileSize"
    :allowed-extensions="['.zip', '.rar', '.7z']"
    :user-id="currentUser.id"
    :auto-start-polling="true"
    :poll-interval="1000"
    @upload-started="handleUploadStarted"
    @upload-progress="handleUploadProgress"
    @upload-completed="handleUploadCompleted"
    @upload-failed="handleUploadFailed"
    @job-status-changed="handleJobStatusChanged"
  />
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import type { Job, UploadProgress } from '@/types'

const config = reactive({
  apiUrl: import.meta.env.VITE_API_BASE_URL,
  chunkSize: 20 * 1024 * 1024, // 20MB
  maxFileSize: 50 * 1024 * 1024 * 1024 // 50GB
})

const currentUser = reactive({
  id: 'user-123'
})

const handleUploadStarted = (file: File) => {
  console.log(`Started uploading: ${file.name} (${file.size} bytes)`)
}

const handleUploadProgress = (progress: UploadProgress) => {
  console.log(`Upload progress: ${progress.percentage}%`)
}

const handleUploadCompleted = (result: any) => {
  console.log('Upload completed:', result)
  // Show success notification
}

const handleUploadFailed = (error: string) => {
  console.error('Upload failed:', error)
  // Show error notification
}

const handleJobStatusChanged = (job: Job) => {
  console.log(`Job ${job.id} status: ${job.status}`)
  if (job.status === 'completed') {
    // Handle completion
  }
}
</script>
```

### Using the Composable Directly
```vue
<script setup lang="ts">
import { ref } from 'vue'
import { useFileUpload } from '@/composables/useFileUpload'

const fileInput = ref<HTMLInputElement>()
const { 
  uploadFile, 
  isUploading, 
  uploadProgress, 
  error,
  currentJob,
  jobs,
  loadJobs 
} = useFileUpload('http://localhost:3000/api')

const handleFileSelect = async (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files?.[0]) {
    try {
      await uploadFile(target.files[0], 'user-123')
      console.log('Upload completed!')
    } catch (err) {
      console.error('Upload failed:', err)
    }
  }
}
</script>
```

## 🎨 Styling

The component uses Tailwind CSS classes. You can customize the appearance by:

1. **Override CSS classes** in your component
2. **Use CSS variables** for colors
3. **Extend Tailwind config** for custom styles

### Custom Styling Example
```vue
<style scoped>
.upload-area {
  @apply border-2 border-dashed border-blue-300 bg-blue-50;
}

.upload-area:hover {
  @apply border-blue-500 bg-blue-100;
}

.progress-bar {
  @apply bg-gradient-to-r from-blue-500 to-purple-600;
}
</style>
```

## 🔍 Troubleshooting

### Common Issues

1. **CORS Errors**
   - Ensure your API server allows CORS from your frontend domain
   - Check `Access-Control-Allow-Origin` headers

2. **File Upload Fails**
   - Verify API endpoints are accessible
   - Check file size limits
   - Ensure proper file extensions

3. **Job Polling Not Working**
   - Check WebSocket/polling configuration
   - Verify job status API endpoints

4. **TypeScript Errors**
   - Ensure all type definitions are imported
   - Check Vue 3 TypeScript setup

### Debug Mode
```typescript
// Enable debug logging
const { uploadFile } = useFileUpload('http://localhost:3000/api')

// Add console logs for debugging
uploadFile(file, 'user-123').catch(console.error)
```

## 📚 API Reference

See `types.ts` for complete TypeScript definitions of all interfaces and types used in the components.

## 🤝 Contributing

1. Follow Vue 3 Composition API patterns
2. Use TypeScript for type safety
3. Follow Tailwind CSS utility-first approach
4. Add proper error handling
5. Include JSDoc comments for functions
