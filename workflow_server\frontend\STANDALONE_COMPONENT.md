# 🚀 FileUploadWorkflowStandalone.vue

Một component Vue 3 hoàn chỉnh, tự chứa (standalone) cho GeoPortal File Upload System. Component này không cần dependencies external và có thể copy trực tiếp vào bất kỳ Vue 3 project nào.

## ✨ Tính năng

- ✅ **Hoàn toàn tự chứa** - Không cần import external files
- ✅ **TypeScript support** đầy đủ
- ✅ **Tailwind CSS** styling tích hợp
- ✅ **Drag & Drop** file upload
- ✅ **Chunk upload** cho file lớn
- ✅ **Progress tracking** realtime
- ✅ **Job monitoring** với polling
- ✅ **Error handling** comprehensive
- ✅ **File validation** (size, extension)
- ✅ **Responsive design**

## 📦 Cách sử dụng

### 1. Copy component vào project

```bash
# Copy file vào thư mục components
cp FileUploadWorkflowStandalone.vue src/components/
```

### 2. Import và sử dụng

```vue
<template>
  <div>
    <FileUploadWorkflowStandalone 
      :api-base-url="apiUrl"
      :chunk-size="chunkSize"
      :max-file-size="maxFileSize"
      :allowed-extensions="allowedExts"
      :user-id="userId"
      :poll-interval="pollInterval"
      @upload-started="onUploadStarted"
      @upload-progress="onUploadProgress"
      @upload-completed="onUploadCompleted"
      @upload-failed="onUploadFailed"
      @job-status-changed="onJobStatusChanged"
    />
  </div>
</template>

<script setup lang="ts">
import FileUploadWorkflowStandalone from '@/components/FileUploadWorkflowStandalone.vue'

// Configuration
const apiUrl = 'http://************:3000/api'
const chunkSize = 10 * 1024 * 1024 // 10MB
const maxFileSize = 100 * 1024 * 1024 * 1024 // 100GB
const allowedExts = ['.zip', '.rar', '.7z', '.tar', '.gz']
const userId = 'current-user-id'
const pollInterval = 2000 // 2 seconds

// Event handlers
const onUploadStarted = (file: File) => {
  console.log('Upload started:', file.name)
}

const onUploadProgress = (progress: any) => {
  console.log('Progress:', progress.percentage + '%')
}

const onUploadCompleted = (result: any) => {
  console.log('Upload completed:', result)
  // Show success notification
}

const onUploadFailed = (error: string) => {
  console.error('Upload failed:', error)
  // Show error notification
}

const onJobStatusChanged = (job: any) => {
  console.log('Job status changed:', job.status)
}
</script>
```

### 3. Sử dụng đơn giản (với defaults)

```vue
<template>
  <FileUploadWorkflowStandalone />
</template>

<script setup lang="ts">
import FileUploadWorkflowStandalone from '@/components/FileUploadWorkflowStandalone.vue'
</script>
```

## ⚙️ Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `apiBaseUrl` | `string` | `'http://************:3000/api'` | API base URL |
| `chunkSize` | `number` | `10485760` (10MB) | Chunk size in bytes |
| `maxFileSize` | `number` | `107374182400` (100GB) | Max file size in bytes |
| `allowedExtensions` | `string[]` | `['.zip', '.rar', '.7z', '.tar', '.gz']` | Allowed file extensions |
| `userId` | `string` | `'vue-client-user'` | User ID for uploads |
| `pollInterval` | `number` | `2000` | Job polling interval in ms |

## 📡 Events

| Event | Payload | Description |
|-------|---------|-------------|
| `upload-started` | `File` | Fired when upload starts |
| `upload-progress` | `UploadProgress` | Fired on upload progress |
| `upload-completed` | `{ file: File, job: Job }` | Fired when upload completes |
| `upload-failed` | `string` | Fired when upload fails |
| `job-status-changed` | `Job` | Fired when job status changes |

## 🎨 Styling

Component sử dụng Tailwind CSS classes. Bạn có thể customize bằng cách:

### 1. Override CSS classes
```vue
<style scoped>
/* Custom styles */
.upload-area {
  @apply border-purple-300 bg-purple-50;
}

.progress-bar {
  @apply bg-gradient-to-r from-purple-500 to-pink-500;
}
</style>
```

### 2. CSS Variables
```vue
<style scoped>
:root {
  --upload-primary-color: #8b5cf6;
  --upload-secondary-color: #a78bfa;
}
</style>
```

## 🔧 Customization Examples

### Custom API URL
```vue
<FileUploadWorkflowStandalone 
  api-base-url="https://your-api.com/api"
/>
```

### Large File Support
```vue
<FileUploadWorkflowStandalone 
  :chunk-size="50 * 1024 * 1024"
  :max-file-size="500 * 1024 * 1024 * 1024"
/>
```

### Custom File Types
```vue
<FileUploadWorkflowStandalone 
  :allowed-extensions="['.zip', '.tar.gz', '.7z']"
/>
```

### Fast Polling
```vue
<FileUploadWorkflowStandalone 
  :poll-interval="1000"
/>
```

## 🚨 Error Handling

Component tự động xử lý các lỗi phổ biến:

- **File size too large**
- **Invalid file extension**
- **Network errors**
- **API errors**
- **Upload timeout**

Tất cả errors được emit qua event `upload-failed`.

## 📱 Responsive Design

Component responsive và hoạt động tốt trên:
- ✅ Desktop (1024px+)
- ✅ Tablet (768px - 1023px)
- ✅ Mobile (320px - 767px)

## 🔍 Debug Mode

Để debug, mở Developer Console và xem logs:

```javascript
// Component tự động log các events quan trọng
console.log('Upload started:', file.name)
console.log('Progress:', progress.percentage + '%')
console.log('Job status changed:', job.status)
```

## 🎯 API Requirements

Component cần các API endpoints sau:

```
POST /api/upload/init-upload
POST /api/upload/upload-chunk
GET  /api/upload/:id/status
GET  /api/jobs
GET  /api/jobs/:id/status
POST /api/jobs/:id/cancel
POST /api/jobs/:id/retry
GET  /api/jobs/stats
```

## 🔒 Security

- File validation trước khi upload
- CSRF protection qua API
- File size limits
- Extension whitelist

## 📊 Performance

- Chunk upload để tối ưu memory
- Progress tracking không block UI
- Efficient polling với cleanup
- Lazy loading cho large file lists

## 🐛 Troubleshooting

### CORS Issues
```javascript
// Đảm bảo server cho phép CORS
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET, POST, PUT, DELETE
Access-Control-Allow-Headers: Content-Type
```

### File Upload Fails
```javascript
// Kiểm tra file size và extension
console.log('File size:', file.size)
console.log('File extension:', file.name.split('.').pop())
```

### Job Polling Not Working
```javascript
// Kiểm tra API endpoint
fetch('/api/jobs/stats').then(r => r.json()).then(console.log)
```

## 📚 TypeScript Support

Component có đầy đủ TypeScript definitions:

```typescript
interface UploadProgress {
  show: boolean
  status: string
  percentage: number
  chunksUploaded: number
  speed: string
}

interface Job {
  id: string
  filename?: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress?: number
  progressMessage?: string
  createdAt: string
  userId?: string
  error?: string
}
```

## 🎉 Ready to Use!

Component này hoàn toàn standalone và ready-to-use. Chỉ cần copy vào project và import là có thể sử dụng ngay!

```vue
<!-- Minimal usage -->
<FileUploadWorkflowStandalone />

<!-- Full configuration -->
<FileUploadWorkflowStandalone 
  api-base-url="https://api.example.com/api"
  :chunk-size="20971520"
  :max-file-size="53687091200"
  :allowed-extensions="['.zip', '.rar']"
  user-id="user-123"
  :poll-interval="1500"
  @upload-completed="handleSuccess"
  @upload-failed="handleError"
/>
```
