const { createWorker } = require('./config/redis');
const fileProcessingService = require('./services/fileProcessingService');
const logger = require('./utils/logger');
const config = require('./config');

/**
 * Processor function cho BullMQ worker
 */
const processFileJob = async (job) => {
  const { jobId, uploadId, filePath, filename, userId } = job.data;
  
  logger.logJob(jobId, 'Job started', { filename, userId });
  
  try {
    // Function để update progress
    const updateProgress = async (progress, message) => {
      await job.updateProgress(progress);
      await job.log(`[${progress}%] ${message}`);
      logger.logJob(jobId, `Progress: ${progress}% - ${message}`);
    };
    
    // Xử lý file
    const result = await fileProcessingService.processFile(job.data, updateProgress);
    
    logger.logJob(jobId, 'Job completed successfully', {
      finalPath: result.finalPath,
      extractedFiles: result.extractedFiles.length,
      totalSize: result.totalSize
    });
    
    return result;
    
  } catch (error) {
    logger.logError('Worker.processFileJob', error, { jobId, filename });
    throw error;
  }
};

/**
 * Khởi tạo và chạy worker
 */
const startWorker = () => {
  try {
    logger.info('Starting file processing worker...');
    
    const worker = createWorker(processFileJob);
    
    // Event handlers
    worker.on('ready', () => {
      logger.info('Worker is ready and waiting for jobs');
    });
    
    worker.on('active', (job) => {
      logger.logJob(job.data.jobId, 'Job started processing', {
        filename: job.data.filename,
        queueJobId: job.id
      });
    });
    
    worker.on('completed', (job, result) => {
      logger.logJob(job.data.jobId, 'Job completed', {
        filename: job.data.filename,
        finalPath: result.finalPath,
        duration: Date.now() - job.processedOn
      });
    });
    
    worker.on('failed', (job, err) => {
      logger.logJob(job.data.jobId, 'Job failed', {
        filename: job.data.filename,
        error: err.message,
        duration: job.processedOn ? Date.now() - job.processedOn : 0
      });
    });
    
    worker.on('progress', (job, progress) => {
      logger.logJob(job.data.jobId, `Progress: ${progress}%`, {
        filename: job.data.filename
      });
    });
    
    worker.on('error', (err) => {
      logger.logError('Worker', err);
    });
    
    // Graceful shutdown
    process.on('SIGTERM', async () => {
      logger.info('Received SIGTERM, shutting down worker gracefully...');
      await worker.close();
      process.exit(0);
    });
    
    process.on('SIGINT', async () => {
      logger.info('Received SIGINT, shutting down worker gracefully...');
      await worker.close();
      process.exit(0);
    });
    
    logger.info(`Worker started with concurrency: ${config.queue.maxConcurrentJobs}`);
    
  } catch (error) {
    logger.logError('Worker.startWorker', error);
    process.exit(1);
  }
};

// Chạy worker nếu file này được execute trực tiếp
if (require.main === module) {
  startWorker();
}

module.exports = {
  startWorker,
  processFileJob
};
