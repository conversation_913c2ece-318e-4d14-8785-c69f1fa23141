const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const path = require('path');

const config = require('./config');
const logger = require('./utils/logger');
const { errorHandler, notFound } = require('./middleware/errorHandler');
const { ensureDirectoryExists } = require('./utils/fileUtils');

// Import routes
const uploadRoutes = require('./routes/upload');
const jobRoutes = require('./routes/jobs');

const app = express();

/**
 * Khởi tạo các thư mục cần thiết
 */
const initDirectories = async () => {
  try {
    await ensureDirectoryExists(config.paths.tempUploadDir);
    await ensureDirectoryExists(config.paths.tempExtractDir);
    await ensureDirectoryExists(config.paths.finalDataDir);
    await ensureDirectoryExists(config.paths.logsDir);
    
    logger.info('All required directories initialized');
  } catch (error) {
    logger.logError('Server.initDirectories', error);
    process.exit(1);
  }
};

/**
 * Middleware setup
 */
const setupMiddleware = () => {
  // Security middleware with relaxed CSP for demo
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: [
          "'self'",
          "'unsafe-inline'",
          "https://unpkg.com",
          "https://cdn.tailwindcss.com"
        ],
        styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.tailwindcss.com"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'", "http://localhost:3000", "http://************:3000"]
      }
    }
  }));
  
  // CORS
  app.use(cors({
    origin: process.env.CORS_ORIGIN || '*',
    credentials: true
  }));
  
  // Logging
  if (config.server.nodeEnv === 'development') {
    app.use(morgan('dev'));
  } else {
    app.use(morgan('combined'));
  }
  
  // Body parsing
  app.use(express.json({ limit: '50mb' }));
  app.use(express.urlencoded({ extended: true, limit: '50mb' }));
  
  // Static files
  app.use('/uploads', express.static(path.join(__dirname, '../public/uploads')));
  
  logger.info('Middleware setup completed');
};

/**
 * Routes setup
 */
const setupRoutes = () => {
  // Health check
  app.get('/health', (req, res) => {
    res.json({
      success: true,
      message: 'GeoPortal File Upload System is running',
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    });
  });
  
  // API routes
  app.use('/api/upload', uploadRoutes);
  app.use('/api/jobs', jobRoutes);
  
  // Serve frontend (if exists)
  if (config.server.nodeEnv === 'production') {
    app.use(express.static(path.join(__dirname, '../public')));
    
    app.get('*', (req, res) => {
      res.sendFile(path.join(__dirname, '../public/index.html'));
    });
  }
  
  logger.info('Routes setup completed');
};

/**
 * Error handling setup
 */
const setupErrorHandling = () => {
  // 404 handler
  app.use(notFound);
  
  // Error handler
  app.use(errorHandler);
  
  logger.info('Error handling setup completed');
};

/**
 * Start server
 */
const startServer = async () => {
  try {
    // Initialize directories
    await initDirectories();
    
    // Setup middleware
    setupMiddleware();
    
    // Setup routes
    setupRoutes();
    
    // Setup error handling
    setupErrorHandling();
    
    // Start listening
    const server = app.listen(config.server.port, () => {
      logger.info(`Server running on port ${config.server.port} in ${config.server.nodeEnv} mode`);
      logger.info(`Health check: http://localhost:${config.server.port}/health`);
    });
    
    // Graceful shutdown
    process.on('SIGTERM', () => {
      logger.info('SIGTERM received, shutting down gracefully');
      server.close(() => {
        logger.info('Server closed');
        process.exit(0);
      });
    });
    
    process.on('SIGINT', () => {
      logger.info('SIGINT received, shutting down gracefully');
      server.close(() => {
        logger.info('Server closed');
        process.exit(0);
      });
    });
    
    // Handle uncaught exceptions
    process.on('uncaughtException', (err) => {
      logger.logError('UncaughtException', err);
      process.exit(1);
    });
    
    process.on('unhandledRejection', (err) => {
      logger.logError('UnhandledRejection', err);
      process.exit(1);
    });
    
  } catch (error) {
    logger.logError('Server.startServer', error);
    process.exit(1);
  }
};

// Start server if this file is run directly
if (require.main === module) {
  startServer();
}

module.exports = app;
