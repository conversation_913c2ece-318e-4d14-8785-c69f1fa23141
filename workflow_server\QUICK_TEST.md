# 🚀 Quick Test Guide - GeoPortal Workflow Server

Hướng dẫn test nhanh để kiểm tra workflow_server có chạy được trên server Ubuntu không.

## 📋 Yêu cầu trước khi test

### 1. Cài đặt Node.js
```bash
# Cài đặt Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Kiểm tra version
node --version  # Phải >= 16.0.0
npm --version
```

### 2. Cài đặt Redis
```bash
# Cài đặt Redis
sudo apt update
sudo apt install redis-server -y

# Khởi động Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server

# Test Redis
redis-cli ping  # Kết quả: PONG
```

### 3. Cài đặt công cụ giải nén (tùy chọn)
```bash
# Cài đặt unrar và 7z
sudo apt install unrar p7zip-full -y
```

## 🏃‍♂️ Chạy test nhanh

### Bước 1: <PERSON><PERSON><PERSON> bị
```bash
# Di chuyển vào thư mục workflow_server
cd workflow_server

# Copy file cấu hình
cp .env.example .env

# Cài đặt dependencies
npm install
```

### Bước 2: Kiểm tra hệ thống
```bash
# Chạy system check
npm run check
```

### Bước 3: Test chạy server
```bash
# Cách 1: Sử dụng script tự động
npm run test:start

# Cách 2: Chạy thủ công
npm start
```

### Bước 4: Test API (terminal khác)
```bash
# Chạy quick test (khi server đang chạy)
npm run test:quick

# Hoặc test thủ công
curl http://localhost:3000/health
```

## 🧪 Test thủ công với curl

### 1. Health Check
```bash
curl http://localhost:3000/health
```
**Kết quả mong đợi:**
```json
{
  "success": true,
  "message": "GeoPortal File Upload System is running",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "1.0.0"
}
```

### 2. Test Upload Init
```bash
curl -X POST http://localhost:3000/api/upload/init-upload \
  -H "Content-Type: application/json" \
  -d '{
    "filename": "test.zip",
    "totalChunks": 1,
    "totalSize": 1024,
    "userId": "test-user"
  }'
```

### 3. Test Jobs API
```bash
# Lấy danh sách jobs
curl http://localhost:3000/api/jobs

# Lấy thống kê jobs
curl http://localhost:3000/api/jobs/stats
```

## 🌐 Test với trình duyệt

Mở trình duyệt và truy cập:
- **Health Check**: http://localhost:3000/health
- **Upload Interface**: http://localhost:3000 (nếu có)

## 🔧 Troubleshooting

### Lỗi thường gặp:

**1. Redis connection failed**
```bash
# Kiểm tra Redis
sudo systemctl status redis-server
redis-cli ping

# Khởi động lại Redis
sudo systemctl restart redis-server
```

**2. Permission denied**
```bash
# Cấp quyền cho thư mục
chmod -R 755 tmp data logs
```

**3. Port 3000 đã được sử dụng**
```bash
# Kiểm tra process đang sử dụng port
sudo netstat -tlnp | grep :3000

# Kill process nếu cần
sudo kill -9 <PID>

# Hoặc đổi port trong .env
echo "PORT=3001" >> .env
```

**4. Node.js version cũ**
```bash
# Cập nhật Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

## 📊 Kết quả mong đợi

Khi test thành công, bạn sẽ thấy:

✅ **System Check**: Tất cả checks pass  
✅ **Server Start**: Server chạy trên port 3000  
✅ **Health Check**: API trả về success  
✅ **Upload API**: Có thể khởi tạo upload  
✅ **Jobs API**: Có thể lấy danh sách jobs  

## 🚀 Bước tiếp theo

Nếu tất cả test đều pass, bạn có thể:

1. **Deploy với PM2**: Sử dụng PM2 để chạy production
2. **Cấu hình Nginx**: Setup reverse proxy
3. **Setup SSL**: Cài đặt HTTPS
4. **Monitoring**: Setup logs và monitoring

## 📞 Hỗ trợ

Nếu gặp vấn đề, kiểm tra:
- Logs trong thư mục `logs/`
- Console output khi chạy server
- System requirements đã đủ chưa
