<template>
  <div class="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">
        GeoPortal File Upload System
      </h1>
      <p class="text-gray-600">
        Upload large files with chunk processing and background job monitoring
      </p>
    </div>

    <!-- Upload Section -->
    <div class="mb-8">
      <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors">
        <div v-if="!selectedFile" class="space-y-4">
          <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
          <div>
            <label for="file-upload" class="cursor-pointer">
              <span class="mt-2 block text-sm font-medium text-gray-900">
                Click to upload or drag and drop
              </span>
              <span class="mt-1 block text-sm text-gray-500">
                ZIP, RAR, 7Z files up to 100GB
              </span>
            </label>
            <input
              id="file-upload"
              name="file-upload"
              type="file"
              class="sr-only"
              accept=".zip,.rar,.7z"
              @change="handleFileSelect"
            />
          </div>
        </div>

        <!-- Selected File Info -->
        <div v-else class="space-y-4">
          <div class="flex items-center justify-center space-x-2">
            <svg class="h-8 w-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span class="text-lg font-medium text-gray-900">{{ selectedFile.name }}</span>
          </div>
          <p class="text-sm text-gray-500">
            Size: {{ formatFileSize(selectedFile.size) }} | 
            Chunks: {{ totalChunks }}
          </p>
          <div class="flex space-x-4 justify-center">
            <button
              @click="startUpload"
              :disabled="isUploading"
              class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {{ isUploading ? 'Uploading...' : 'Start Upload' }}
            </button>
            <button
              @click="clearFile"
              :disabled="isUploading"
              class="px-6 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              Clear
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Upload Progress -->
    <div v-if="uploadProgress.show" class="mb-8">
      <div class="bg-gray-50 rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Upload Progress</h3>
        
        <!-- Progress Bar -->
        <div class="mb-4">
          <div class="flex justify-between text-sm text-gray-600 mb-1">
            <span>{{ uploadProgress.status }}</span>
            <span>{{ uploadProgress.percentage }}%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div 
              class="bg-blue-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: uploadProgress.percentage + '%' }"
            ></div>
          </div>
        </div>

        <!-- Upload Details -->
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span class="text-gray-500">Chunks uploaded:</span>
            <span class="ml-2 font-medium">{{ uploadProgress.chunksUploaded }} / {{ totalChunks }}</span>
          </div>
          <div>
            <span class="text-gray-500">Speed:</span>
            <span class="ml-2 font-medium">{{ uploadProgress.speed }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Job Status -->
    <div v-if="currentJob" class="mb-8">
      <div class="bg-gray-50 rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Processing Status</h3>
        
        <div class="flex items-center space-x-3 mb-4">
          <div class="flex-shrink-0">
            <div v-if="currentJob.status === 'running'" class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <div v-else-if="currentJob.status === 'completed'" class="h-6 w-6 bg-green-500 rounded-full flex items-center justify-center">
              <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <div v-else-if="currentJob.status === 'failed'" class="h-6 w-6 bg-red-500 rounded-full flex items-center justify-center">
              <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
          </div>
          <div>
            <p class="font-medium text-gray-900">{{ getJobStatusText(currentJob.status) }}</p>
            <p class="text-sm text-gray-500">Job ID: {{ currentJob.id }}</p>
          </div>
        </div>

        <!-- Job Progress -->
        <div v-if="currentJob.progress !== undefined" class="mb-4">
          <div class="flex justify-between text-sm text-gray-600 mb-1">
            <span>{{ currentJob.progressMessage || 'Processing...' }}</span>
            <span>{{ currentJob.progress }}%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div 
              class="bg-green-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: currentJob.progress + '%' }"
            ></div>
          </div>
        </div>

        <!-- Job Actions -->
        <div class="flex space-x-3">
          <button
            v-if="currentJob.status === 'failed'"
            @click="retryJob"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
          >
            Retry
          </button>
          <button
            v-if="currentJob.status === 'running'"
            @click="cancelJob"
            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors text-sm"
          >
            Cancel
          </button>
          <button
            @click="refreshJobStatus"
            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors text-sm"
          >
            Refresh
          </button>
        </div>
      </div>
    </div>

    <!-- Recent Jobs -->
    <div class="mb-8">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">Recent Jobs</h3>
        <button
          @click="loadJobs"
          class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors text-sm"
        >
          Refresh
        </button>
      </div>

      <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
        <div v-if="jobs.length === 0" class="p-6 text-center text-gray-500">
          No jobs found
        </div>
        <div v-else class="divide-y divide-gray-200">
          <div
            v-for="job in jobs"
            :key="job.id"
            class="p-4 hover:bg-gray-50 transition-colors cursor-pointer"
            @click="selectJob(job)"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <div :class="getJobStatusClass(job.status)" class="h-3 w-3 rounded-full"></div>
                </div>
                <div>
                  <p class="font-medium text-gray-900">{{ job.filename || 'Unknown file' }}</p>
                  <p class="text-sm text-gray-500">{{ formatDate(job.createdAt) }}</p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-sm font-medium" :class="getJobStatusTextClass(job.status)">
                  {{ getJobStatusText(job.status) }}
                </p>
                <p class="text-xs text-gray-500">{{ job.id.substring(0, 8) }}...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Error Messages -->
    <div v-if="error" class="mb-4">
      <div class="bg-red-50 border border-red-200 rounded-md p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Error</h3>
            <p class="mt-1 text-sm text-red-700">{{ error }}</p>
          </div>
          <div class="ml-auto pl-3">
            <button
              @click="clearError"
              class="inline-flex text-red-400 hover:text-red-600"
            >
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'

// Types
interface UploadProgress {
  show: boolean
  status: string
  percentage: number
  chunksUploaded: number
  speed: string
}

interface Job {
  id: string
  filename?: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress?: number
  progressMessage?: string
  createdAt: string
  userId?: string
}

interface UploadSession {
  uploadId: string
  chunkSize: number
  expiresAt: string
}

// Reactive state
const selectedFile = ref<File | null>(null)
const isUploading = ref(false)
const error = ref<string | null>(null)
const currentJob = ref<Job | null>(null)
const jobs = ref<Job[]>([])
const uploadSession = ref<UploadSession | null>(null)

const uploadProgress = reactive<UploadProgress>({
  show: false,
  status: 'Preparing...',
  percentage: 0,
  chunksUploaded: 0,
  speed: '0 MB/s'
})

// Configuration
const API_BASE_URL = 'http://************:3000/api'
const CHUNK_SIZE = 10 * 1024 * 1024 // 10MB
const POLL_INTERVAL = 2000 // 2 seconds

// Computed
const totalChunks = computed(() => {
  if (!selectedFile.value) return 0
  return Math.ceil(selectedFile.value.size / CHUNK_SIZE)
})

// Polling interval
let jobPollingInterval: number | null = null

// Methods
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    selectedFile.value = target.files[0]
    clearError()
  }
}

const clearFile = () => {
  selectedFile.value = null
  uploadSession.value = null
  uploadProgress.show = false
  uploadProgress.percentage = 0
  uploadProgress.chunksUploaded = 0
}

const clearError = () => {
  error.value = null
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleString()
}

const getJobStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: 'Pending',
    running: 'Processing',
    completed: 'Completed',
    failed: 'Failed'
  }
  return statusMap[status] || status
}

const getJobStatusClass = (status: string): string => {
  const classMap: Record<string, string> = {
    pending: 'bg-yellow-400',
    running: 'bg-blue-400',
    completed: 'bg-green-400',
    failed: 'bg-red-400'
  }
  return classMap[status] || 'bg-gray-400'
}

const getJobStatusTextClass = (status: string): string => {
  const classMap: Record<string, string> = {
    pending: 'text-yellow-600',
    running: 'text-blue-600',
    completed: 'text-green-600',
    failed: 'text-red-600'
  }
  return classMap[status] || 'text-gray-600'
}

// API Methods
const apiRequest = async (endpoint: string, options: RequestInit = {}): Promise<any> => {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    return await response.json()
  } catch (err) {
    console.error('API Request failed:', err)
    throw err
  }
}

const initializeUpload = async (): Promise<UploadSession> => {
  if (!selectedFile.value) {
    throw new Error('No file selected')
  }

  const payload = {
    filename: selectedFile.value.name,
    totalChunks: totalChunks.value,
    totalSize: selectedFile.value.size,
    userId: 'vue-client-user'
  }

  const response = await apiRequest('/upload/init-upload', {
    method: 'POST',
    body: JSON.stringify(payload)
  })

  if (!response.success) {
    throw new Error(response.error || 'Failed to initialize upload')
  }

  return response.data
}

const uploadChunk = async (chunkIndex: number, chunk: Blob): Promise<any> => {
  if (!uploadSession.value) {
    throw new Error('No upload session')
  }

  const formData = new FormData()
  formData.append('uploadId', uploadSession.value.uploadId)
  formData.append('chunkIndex', chunkIndex.toString())
  formData.append('chunk', chunk)

  const response = await fetch(`${API_BASE_URL}/upload/upload-chunk`, {
    method: 'POST',
    body: formData
  })

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
  }

  const result = await response.json()
  if (!result.success) {
    throw new Error(result.error || 'Failed to upload chunk')
  }

  return result.data
}

const startUpload = async () => {
  if (!selectedFile.value) {
    error.value = 'Please select a file first'
    return
  }

  try {
    isUploading.value = true
    uploadProgress.show = true
    uploadProgress.status = 'Initializing upload...'
    uploadProgress.percentage = 0
    uploadProgress.chunksUploaded = 0
    clearError()

    // Initialize upload session
    uploadSession.value = await initializeUpload()

    uploadProgress.status = 'Uploading chunks...'

    const startTime = Date.now()
    let uploadedBytes = 0

    // Upload chunks
    for (let i = 0; i < totalChunks.value; i++) {
      const start = i * CHUNK_SIZE
      const end = Math.min(start + CHUNK_SIZE, selectedFile.value.size)
      const chunk = selectedFile.value.slice(start, end)

      const result = await uploadChunk(i, chunk)

      uploadedBytes += chunk.size
      uploadProgress.chunksUploaded = i + 1
      uploadProgress.percentage = Math.round((uploadProgress.chunksUploaded / totalChunks.value) * 100)

      // Calculate upload speed
      const elapsed = (Date.now() - startTime) / 1000
      const speed = uploadedBytes / elapsed
      uploadProgress.speed = formatFileSize(speed) + '/s'

      uploadProgress.status = `Uploading chunk ${i + 1} of ${totalChunks.value}...`

      // If upload completed and job created
      if (result.isComplete && result.jobId) {
        currentJob.value = {
          id: result.jobId,
          filename: selectedFile.value.name,
          status: 'pending',
          createdAt: new Date().toISOString()
        }
        startJobPolling()
        break
      }
    }

    uploadProgress.status = 'Upload completed!'

    // Load jobs list
    await loadJobs()

  } catch (err) {
    console.error('Upload failed:', err)
    error.value = err instanceof Error ? err.message : 'Upload failed'
  } finally {
    isUploading.value = false
  }
}

const loadJobs = async () => {
  try {
    const response = await apiRequest('/jobs?limit=10&offset=0')
    if (response.success) {
      jobs.value = response.data.jobs || []
    }
  } catch (err) {
    console.error('Failed to load jobs:', err)
  }
}

const getJobStatus = async (jobId: string): Promise<Job | null> => {
  try {
    const response = await apiRequest(`/jobs/${jobId}/status`)
    if (response.success) {
      return response.data
    }
  } catch (err) {
    console.error('Failed to get job status:', err)
  }
  return null
}

const refreshJobStatus = async () => {
  if (!currentJob.value) return

  const status = await getJobStatus(currentJob.value.id)
  if (status) {
    currentJob.value = { ...currentJob.value, ...status }
  }
}

const selectJob = async (job: Job) => {
  currentJob.value = job
  const status = await getJobStatus(job.id)
  if (status) {
    currentJob.value = { ...currentJob.value, ...status }
  }

  if (job.status === 'running') {
    startJobPolling()
  }
}

const cancelJob = async () => {
  if (!currentJob.value) return

  try {
    await apiRequest(`/jobs/${currentJob.value.id}/cancel`, { method: 'POST' })
    await refreshJobStatus()
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to cancel job'
  }
}

const retryJob = async () => {
  if (!currentJob.value) return

  try {
    await apiRequest(`/jobs/${currentJob.value.id}/retry`, { method: 'POST' })
    await refreshJobStatus()
    startJobPolling()
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to retry job'
  }
}

const startJobPolling = () => {
  if (jobPollingInterval) {
    clearInterval(jobPollingInterval)
  }

  jobPollingInterval = setInterval(async () => {
    if (!currentJob.value) {
      clearInterval(jobPollingInterval!)
      return
    }

    await refreshJobStatus()

    // Stop polling if job is completed or failed
    if (currentJob.value.status === 'completed' || currentJob.value.status === 'failed') {
      clearInterval(jobPollingInterval!)
      jobPollingInterval = null
      await loadJobs() // Refresh jobs list
    }
  }, POLL_INTERVAL)
}

// Lifecycle
onMounted(() => {
  loadJobs()
})

onUnmounted(() => {
  if (jobPollingInterval) {
    clearInterval(jobPollingInterval)
  }
})
