const jobService = require('../services/jobService');
const { asyncHandler } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

/**
 * GET /api/jobs/:id/status
 * <PERSON><PERSON><PERSON> trạng thái job theo ID
 */
const getJobStatus = asyncHandler(async (req, res) => {
  const { id: jobId } = req.params;

  if (!jobId) {
    return res.status(400).json({
      success: false,
      error: 'Job ID is required'
    });
  }

  try {
    const jobStatus = await jobService.getJobStatus(jobId);
    
    res.json({
      success: true,
      data: jobStatus
    });
  } catch (error) {
    if (error.message === 'Job not found') {
      return res.status(404).json({
        success: false,
        error: 'Job not found'
      });
    }
    
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/jobs
 * <PERSON><PERSON><PERSON> danh sách jobs với phân trang và filter
 */
const getJobs = asyncHandler(async (req, res) => {
  const {
    status,
    limit = 20,
    offset = 0,
    userId
  } = req.query;

  try {
    const options = {
      limit: parseInt(limit),
      offset: parseInt(offset)
    };

    // Filter theo status nếu có
    if (status) {
      const validStatuses = ['waiting', 'active', 'completed', 'failed'];
      const statusArray = status.split(',').filter(s => validStatuses.includes(s));
      if (statusArray.length > 0) {
        options.status = statusArray;
      }
    }

    const result = await jobService.getJobs(options);
    
    // Filter theo userId nếu có (client-side filtering vì BullMQ không hỗ trợ)
    if (userId) {
      result.jobs = result.jobs.filter(job => job.userId === userId);
    }

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/jobs/:id/cancel
 * Hủy job
 */
const cancelJob = asyncHandler(async (req, res) => {
  const { id: jobId } = req.params;

  if (!jobId) {
    return res.status(400).json({
      success: false,
      error: 'Job ID is required'
    });
  }

  try {
    const result = await jobService.cancelJob(jobId);
    
    logger.logJob(jobId, 'Job cancelled via API');
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    if (error.message.includes('not found')) {
      return res.status(404).json({
        success: false,
        error: error.message
      });
    }
    
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/jobs/:id/retry
 * Retry failed job
 */
const retryJob = asyncHandler(async (req, res) => {
  const { id: jobId } = req.params;

  if (!jobId) {
    return res.status(400).json({
      success: false,
      error: 'Job ID is required'
    });
  }

  try {
    const result = await jobService.retryJob(jobId);
    
    logger.logJob(jobId, 'Job retried via API');
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    if (error.message.includes('not found')) {
      return res.status(404).json({
        success: false,
        error: error.message
      });
    }
    
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/jobs/stats
 * Lấy thống kê jobs
 */
const getJobStats = asyncHandler(async (req, res) => {
  try {
    const allJobs = await jobService.getJobs({ limit: 1000 });
    
    const stats = {
      total: allJobs.jobs.length,
      pending: allJobs.jobs.filter(job => job.status === 'pending').length,
      running: allJobs.jobs.filter(job => job.status === 'running').length,
      completed: allJobs.jobs.filter(job => job.status === 'completed').length,
      failed: allJobs.jobs.filter(job => job.status === 'failed').length
    };
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = {
  getJobStatus,
  getJobs,
  cancelJob,
  retryJob,
  getJobStats
};
