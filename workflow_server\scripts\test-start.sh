#!/bin/bash

# Script để test chạy workflow_server đơn giản
echo "🚀 Starting GeoPortal Workflow Server Test"
echo "========================================"

# Kiểm tra Node.js
echo "📦 Checking Node.js..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed"
    exit 1
fi
echo "✅ Node.js version: $(node --version)"

# Kiểm tra npm
echo "📦 Checking npm..."
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed"
    exit 1
fi
echo "✅ npm version: $(npm --version)"

# Kiểm tra Redis
echo "🔴 Checking Redis..."
if ! command -v redis-cli &> /dev/null; then
    echo "❌ Redis is not installed"
    echo "Install with: sudo apt install redis-server"
    exit 1
fi

# Test Redis connection
if ! redis-cli ping &> /dev/null; then
    echo "❌ Redis is not running"
    echo "Start with: sudo systemctl start redis-server"
    exit 1
fi
echo "✅ Redis is running"

# Kiểm tra dependencies
echo "📦 Checking dependencies..."
if [ ! -d "node_modules" ]; then
    echo "⚠️  node_modules not found, installing dependencies..."
    npm install
fi
echo "✅ Dependencies OK"

# Tạo thư mục cần thiết
echo "📁 Creating directories..."
mkdir -p tmp/uploads_bigfiles tmp/extracted data/final logs
echo "✅ Directories created"

# Chạy system check
echo "🔍 Running system check..."
node scripts/check-system.js
if [ $? -ne 0 ]; then
    echo "❌ System check failed"
    exit 1
fi

echo ""
echo "🎉 All checks passed! Starting server..."
echo "========================================"
echo ""
echo "📝 Server will start on: http://localhost:3000"
echo "📝 Health check: http://localhost:3000/health"
echo "📝 Press Ctrl+C to stop"
echo ""

# Start server
npm start
