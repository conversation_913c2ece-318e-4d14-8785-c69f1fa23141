#!/usr/bin/env node

const http = require('http');
const fs = require('fs-extra');
const path = require('path');

/**
 * Script test nhanh để kiểm tra server có chạy đư<PERSON>c không
 */
class QuickTest {
  constructor() {
    this.baseUrl = 'http://localhost:3000';
    this.testResults = [];
  }

  async runAllTests() {
    console.log('🚀 Starting Quick Test for GeoPortal Workflow Server\n');
    
    // Kiểm tra server có chạy không
    const serverRunning = await this.checkServerRunning();
    if (!serverRunning) {
      console.log('❌ Server is not running. Please start the server first:');
      console.log('   npm start');
      process.exit(1);
    }

    // Chạy các test
    await this.testHealthCheck();
    await this.testInitUpload();
    await this.testJobsAPI();
    
    // In kết quả
    this.printResults();
  }

  async checkServerRunning() {
    console.log('🔍 Checking if server is running...');
    try {
      await this.makeRequest('GET', '/health');
      console.log('✅ Server is running\n');
      return true;
    } catch (error) {
      console.log('❌ Server is not running\n');
      return false;
    }
  }

  async testHealthCheck() {
    console.log('🏥 Testing Health Check...');
    try {
      const response = await this.makeRequest('GET', '/health');
      const data = JSON.parse(response);
      
      if (data.success && data.message) {
        this.testResults.push({ test: 'Health Check', status: 'PASS', message: 'OK' });
        console.log('✅ Health check passed');
      } else {
        this.testResults.push({ test: 'Health Check', status: 'FAIL', message: 'Invalid response format' });
        console.log('❌ Health check failed - Invalid response');
      }
    } catch (error) {
      this.testResults.push({ test: 'Health Check', status: 'FAIL', message: error.message });
      console.log('❌ Health check failed:', error.message);
    }
  }

  async testInitUpload() {
    console.log('\n📤 Testing Upload Initialization...');
    try {
      const payload = JSON.stringify({
        filename: 'test.zip',
        totalChunks: 1,
        totalSize: 1024,
        userId: 'test-user'
      });

      const response = await this.makeRequest('POST', '/api/upload/init-upload', payload, {
        'Content-Type': 'application/json'
      });
      
      const data = JSON.parse(response);
      
      if (data.success && data.data && data.data.uploadId) {
        this.testResults.push({ test: 'Upload Init', status: 'PASS', message: `Upload ID: ${data.data.uploadId}` });
        console.log('✅ Upload initialization passed');
        
        // Test get upload status
        await this.testUploadStatus(data.data.uploadId);
      } else {
        this.testResults.push({ test: 'Upload Init', status: 'FAIL', message: 'Invalid response format' });
        console.log('❌ Upload initialization failed - Invalid response');
      }
    } catch (error) {
      this.testResults.push({ test: 'Upload Init', status: 'FAIL', message: error.message });
      console.log('❌ Upload initialization failed:', error.message);
    }
  }

  async testUploadStatus(uploadId) {
    console.log('📊 Testing Upload Status...');
    try {
      const response = await this.makeRequest('GET', `/api/upload/${uploadId}/status`);
      const data = JSON.parse(response);
      
      if (data.success && data.data && data.data.uploadId === uploadId) {
        this.testResults.push({ test: 'Upload Status', status: 'PASS', message: 'OK' });
        console.log('✅ Upload status check passed');
      } else {
        this.testResults.push({ test: 'Upload Status', status: 'FAIL', message: 'Invalid response format' });
        console.log('❌ Upload status check failed');
      }
    } catch (error) {
      this.testResults.push({ test: 'Upload Status', status: 'FAIL', message: error.message });
      console.log('❌ Upload status check failed:', error.message);
    }
  }

  async testJobsAPI() {
    console.log('\n⚙️  Testing Jobs API...');
    try {
      const response = await this.makeRequest('GET', '/api/jobs');
      const data = JSON.parse(response);
      
      if (data.success && Array.isArray(data.data.jobs)) {
        this.testResults.push({ test: 'Jobs API', status: 'PASS', message: `Found ${data.data.jobs.length} jobs` });
        console.log('✅ Jobs API passed');
        
        // Test job stats
        await this.testJobStats();
      } else {
        this.testResults.push({ test: 'Jobs API', status: 'FAIL', message: 'Invalid response format' });
        console.log('❌ Jobs API failed');
      }
    } catch (error) {
      this.testResults.push({ test: 'Jobs API', status: 'FAIL', message: error.message });
      console.log('❌ Jobs API failed:', error.message);
    }
  }

  async testJobStats() {
    console.log('📈 Testing Job Stats...');
    try {
      const response = await this.makeRequest('GET', '/api/jobs/stats');
      const data = JSON.parse(response);
      
      if (data.success && data.data && typeof data.data.total === 'number') {
        this.testResults.push({ test: 'Job Stats', status: 'PASS', message: `Total jobs: ${data.data.total}` });
        console.log('✅ Job stats passed');
      } else {
        this.testResults.push({ test: 'Job Stats', status: 'FAIL', message: 'Invalid response format' });
        console.log('❌ Job stats failed');
      }
    } catch (error) {
      this.testResults.push({ test: 'Job Stats', status: 'FAIL', message: error.message });
      console.log('❌ Job stats failed:', error.message);
    }
  }

  makeRequest(method, path, data = null, headers = {}) {
    return new Promise((resolve, reject) => {
      const url = new URL(path, this.baseUrl);
      const options = {
        method,
        headers: {
          'User-Agent': 'QuickTest/1.0',
          ...headers
        }
      };

      const req = http.request(url, options, (res) => {
        let body = '';
        res.on('data', chunk => body += chunk);
        res.on('end', () => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(body);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${body}`));
          }
        });
      });

      req.on('error', reject);
      
      if (data) {
        req.write(data);
      }
      
      req.end();
    });
  }

  printResults() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 QUICK TEST RESULTS');
    console.log('='.repeat(60));

    const passed = this.testResults.filter(r => r.status === 'PASS').length;
    const failed = this.testResults.filter(r => r.status === 'FAIL').length;

    this.testResults.forEach(result => {
      const icon = result.status === 'PASS' ? '✅' : '❌';
      console.log(`${icon} ${result.test.padEnd(20)} - ${result.message}`);
    });

    console.log('='.repeat(60));
    console.log(`📈 Summary: ${passed} passed, ${failed} failed`);
    
    if (failed === 0) {
      console.log('🎉 All tests passed! Server is working correctly.');
    } else {
      console.log('⚠️  Some tests failed. Check the server logs for details.');
    }
    
    console.log('='.repeat(60));
  }
}

// Chạy test nếu file này được execute trực tiếp
if (require.main === module) {
  const tester = new QuickTest();
  tester.runAllTests().catch(console.error);
}

module.exports = QuickTest;
