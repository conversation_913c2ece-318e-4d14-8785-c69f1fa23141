{"name": "geoportal-file-upload-system", "version": "1.0.0", "description": "<PERSON><PERSON> thống upload file lớn với xử lý tác vụ ngầm cho GeoPortal", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "worker": "node src/worker.js", "test": "jest", "test:quick": "node scripts/quick-test.js", "test:start": "bash scripts/test-start.sh", "check": "node scripts/check-system.js", "cleanup": "node scripts/cleanup.js", "cleanup:dry": "node scripts/cleanup.js --dry-run", "cleanup:all": "node scripts/cleanup.js --clean-logs --clean-final", "setup": "npm run check && npm install", "pm2:start": "pm2 start ecosystem.config.js --env production", "pm2:stop": "pm2 stop geoportal-server geoportal-worker", "pm2:restart": "pm2 restart geoportal-server geoportal-worker", "pm2:delete": "pm2 delete geoportal-server geoportal-worker", "pm2:logs": "pm2 logs", "pm2:status": "pm2 status"}, "keywords": ["upload", "chunk", "background-jobs", "geoportal"], "author": "GeoPortal Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "multer": "^1.4.5-lts.1", "fs-extra": "^11.1.1", "bullmq": "^4.15.4", "redis": "^4.6.10", "unzipper": "^0.10.14", "node-7z": "^3.0.0", "uuid": "^9.0.1", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}}