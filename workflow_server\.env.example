# GeoPortal Workflow Server Configuration

# Server Configuration
NODE_ENV=development
PORT=3000

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
# REDIS_PASSWORD=your_password_here

# File Upload Configuration
CHUNK_SIZE=10485760
MAX_FILE_SIZE=107374182400
UPLOAD_TIMEOUT=300000

# Directory Paths
TEMP_UPLOAD_DIR=./tmp/uploads_bigfiles
TEMP_EXTRACT_DIR=./tmp/extracted
FINAL_DATA_DIR=./data/final

# Job Queue Configuration
QUEUE_NAME=file-processing-queue
MAX_CONCURRENT_JOBS=3
JOB_TIMEOUT=1800000

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# CORS Configuration
CORS_ORIGIN=*
