const { createQueue } = require('../config/redis');
const { generateJobId, generateTimestamp } = require('../utils/fileUtils');
const logger = require('../utils/logger');

class JobService {
  constructor() {
    this.queue = null;
    this.initQueue();
  }

  async initQueue() {
    try {
      this.queue = createQueue();
      logger.info('Job queue initialized successfully');
    } catch (error) {
      logger.logError('JobService.initQueue', error);
      throw error;
    }
  }

  /**
   * Tạo job xử lý file
   */
  async createProcessingJob(jobData) {
    try {
      if (!this.queue) {
        await this.initQueue();
      }

      const jobId = generateJobId();
      const timestamp = generateTimestamp();

      const jobPayload = {
        jobId,
        uploadId: jobData.uploadId,
        filePath: jobData.filePath,
        filename: jobData.filename,
        userId: jobData.userId || 'anonymous',
        timestamp,
        createdAt: new Date().toISOString(),
        status: 'pending'
      };

      // Thêm job vào queue
      const job = await this.queue.add('process-file', jobPayload, {
        jobId: jobId,
        removeOnComplete: 50,
        removeOnFail: 100,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000
        }
      });

      logger.logJob(jobId, 'Processing job created', {
        uploadId: jobData.uploadId,
        filename: jobData.filename,
        userId: jobData.userId
      });

      return {
        jobId,
        queueJobId: job.id,
        status: 'pending'
      };
    } catch (error) {
      logger.logError('JobService.createProcessingJob', error);
      throw error;
    }
  }

  /**
   * Lấy trạng thái job
   */
  async getJobStatus(jobId) {
    try {
      if (!this.queue) {
        await this.initQueue();
      }

      // Tìm job trong queue
      const jobs = await this.queue.getJobs(['waiting', 'active', 'completed', 'failed']);
      const job = jobs.find(j => j.data.jobId === jobId);

      if (!job) {
        throw new Error('Job not found');
      }

      const jobState = await job.getState();
      const progress = job.progress || 0;
      const logs = job.logs || [];

      let status = 'pending';
      switch (jobState) {
        case 'waiting':
          status = 'pending';
          break;
        case 'active':
          status = 'running';
          break;
        case 'completed':
          status = 'completed';
          break;
        case 'failed':
          status = 'failed';
          break;
      }

      const result = {
        jobId,
        status,
        progress,
        createdAt: job.data.createdAt,
        processedAt: job.processedOn ? new Date(job.processedOn).toISOString() : null,
        finishedAt: job.finishedOn ? new Date(job.finishedOn).toISOString() : null,
        logs: logs.slice(-10), // Lấy 10 log gần nhất
        data: job.data
      };

      // Thêm thông tin lỗi nếu job failed
      if (jobState === 'failed' && job.failedReason) {
        result.error = job.failedReason;
      }

      // Thêm kết quả nếu job completed
      if (jobState === 'completed' && job.returnvalue) {
        result.result = job.returnvalue;
      }

      return result;
    } catch (error) {
      logger.logError('JobService.getJobStatus', error, { jobId });
      throw error;
    }
  }

  /**
   * Lấy danh sách jobs
   */
  async getJobs(options = {}) {
    try {
      if (!this.queue) {
        await this.initQueue();
      }

      const {
        status = ['waiting', 'active', 'completed', 'failed'],
        limit = 50,
        offset = 0
      } = options;

      const jobs = await this.queue.getJobs(status, offset, offset + limit - 1);
      
      const jobList = jobs.map(job => ({
        jobId: job.data.jobId,
        queueJobId: job.id,
        status: job.finishedOn ? (job.failedReason ? 'failed' : 'completed') : 
                job.processedOn ? 'running' : 'pending',
        filename: job.data.filename,
        userId: job.data.userId,
        createdAt: job.data.createdAt,
        processedAt: job.processedOn ? new Date(job.processedOn).toISOString() : null,
        finishedAt: job.finishedOn ? new Date(job.finishedOn).toISOString() : null,
        progress: job.progress || 0
      }));

      return {
        jobs: jobList,
        total: jobList.length,
        offset,
        limit
      };
    } catch (error) {
      logger.logError('JobService.getJobs', error);
      throw error;
    }
  }

  /**
   * Hủy job
   */
  async cancelJob(jobId) {
    try {
      if (!this.queue) {
        await this.initQueue();
      }

      const jobs = await this.queue.getJobs(['waiting', 'active']);
      const job = jobs.find(j => j.data.jobId === jobId);

      if (!job) {
        throw new Error('Job not found or already completed');
      }

      await job.remove();
      
      logger.logJob(jobId, 'Job cancelled');
      
      return { success: true, message: 'Job cancelled successfully' };
    } catch (error) {
      logger.logError('JobService.cancelJob', error, { jobId });
      throw error;
    }
  }

  /**
   * Retry failed job
   */
  async retryJob(jobId) {
    try {
      if (!this.queue) {
        await this.initQueue();
      }

      const jobs = await this.queue.getJobs(['failed']);
      const job = jobs.find(j => j.data.jobId === jobId);

      if (!job) {
        throw new Error('Failed job not found');
      }

      await job.retry();
      
      logger.logJob(jobId, 'Job retried');
      
      return { success: true, message: 'Job retried successfully' };
    } catch (error) {
      logger.logError('JobService.retryJob', error, { jobId });
      throw error;
    }
  }
}

module.exports = new JobService();
