// Type definitions for GeoPortal File Upload System

export interface UploadProgress {
  show: boolean
  status: string
  percentage: number
  chunksUploaded: number
  speed: string
}

export interface Job {
  id: string
  filename?: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress?: number
  progressMessage?: string
  createdAt: string
  updatedAt?: string
  completedAt?: string
  userId?: string
  error?: string
  result?: JobResult
}

export interface JobResult {
  success: boolean
  finalPath?: string
  extractedFiles?: string[]
  totalSize?: number
  processedAt?: string
}

export interface UploadSession {
  uploadId: string
  chunkSize: number
  expiresAt: string
}

export interface UploadInitRequest {
  filename: string
  totalChunks: number
  totalSize: number
  userId: string
}

export interface UploadChunkRequest {
  uploadId: string
  chunkIndex: number
  chunk: File | Blob
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface JobStats {
  total: number
  pending: number
  running: number
  completed: number
  failed: number
}

export interface UploadInfo {
  uploadId: string
  filename: string
  status: 'pending' | 'uploading' | 'completed' | 'failed'
  chunksReceived: number
  totalChunks: number
  progress: number
  createdAt: string
  lastChunkAt?: string
  completedAt?: string
}

// API Client Configuration
export interface ApiConfig {
  baseUrl: string
  timeout?: number
  headers?: Record<string, string>
}

// File Upload Configuration
export interface UploadConfig {
  chunkSize: number
  maxFileSize: number
  allowedExtensions: string[]
  maxConcurrentUploads: number
}

// Component Props
export interface FileUploadWorkflowProps {
  apiBaseUrl?: string
  chunkSize?: number
  maxFileSize?: number
  allowedExtensions?: string[]
  userId?: string
  autoStartPolling?: boolean
  pollInterval?: number
}

// Component Emits
export interface FileUploadWorkflowEmits {
  'upload-started': [file: File]
  'upload-progress': [progress: UploadProgress]
  'upload-completed': [result: any]
  'upload-failed': [error: string]
  'job-status-changed': [job: Job]
  'job-completed': [job: Job]
  'job-failed': [job: Job]
}

// Utility Types
export type JobStatus = Job['status']
export type UploadStatus = UploadInfo['status']

// Error Types
export class UploadError extends Error {
  constructor(
    message: string,
    public code?: string,
    public details?: any
  ) {
    super(message)
    this.name = 'UploadError'
  }
}

export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public response?: any
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

// Constants
export const UPLOAD_STATUS = {
  PENDING: 'pending' as const,
  UPLOADING: 'uploading' as const,
  COMPLETED: 'completed' as const,
  FAILED: 'failed' as const
}

export const JOB_STATUS = {
  PENDING: 'pending' as const,
  RUNNING: 'running' as const,
  COMPLETED: 'completed' as const,
  FAILED: 'failed' as const
}

export const DEFAULT_CONFIG: UploadConfig = {
  chunkSize: 10 * 1024 * 1024, // 10MB
  maxFileSize: 100 * 1024 * 1024 * 1024, // 100GB
  allowedExtensions: ['.zip', '.rar', '.7z', '.tar', '.gz'],
  maxConcurrentUploads: 3
}
