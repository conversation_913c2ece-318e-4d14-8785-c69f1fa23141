#!/bin/bash

# Script để start/stop/restart PM2 apps cho GeoPortal
echo "🚀 GeoPortal PM2 Management Script"
echo "=================================="

# Kiểm tra PM2 đã cài chưa
if ! command -v pm2 &> /dev/null; then
    echo "❌ PM2 is not installed"
    echo "Install with: npm install -g pm2"
    exit 1
fi

# Function để hiển thị help
show_help() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     - Start all applications"
    echo "  stop      - Stop all applications"
    echo "  restart   - Restart all applications"
    echo "  status    - Show status of all applications"
    echo "  logs      - Show logs"
    echo "  delete    - Delete all applications from PM2"
    echo "  setup     - Setup PM2 startup script"
    echo ""
}

# Function để start apps
start_apps() {
    echo "🚀 Starting GeoPortal applications..."
    
    # Tạo thư mục logs nếu chưa có
    mkdir -p logs
    
    # Start với ecosystem file
    pm2 start ecosystem.config.js --env production
    
    # Save PM2 configuration
    pm2 save
    
    echo "✅ Applications started successfully!"
    pm2 status
}

# Function để stop apps
stop_apps() {
    echo "🛑 Stopping GeoPortal applications..."
    pm2 stop geoportal-server geoportal-worker
    echo "✅ Applications stopped!"
}

# Function để restart apps
restart_apps() {
    echo "🔄 Restarting GeoPortal applications..."
    pm2 restart geoportal-server geoportal-worker
    echo "✅ Applications restarted!"
}

# Function để delete apps
delete_apps() {
    echo "🗑️  Deleting GeoPortal applications from PM2..."
    pm2 delete geoportal-server geoportal-worker 2>/dev/null || true
    echo "✅ Applications deleted!"
}

# Function để setup startup
setup_startup() {
    echo "⚙️  Setting up PM2 startup script..."
    pm2 startup
    echo ""
    echo "📝 Please run the command shown above with sudo"
    echo "📝 Then run: pm2 save"
}

# Main logic
case "${1:-help}" in
    start)
        start_apps
        ;;
    stop)
        stop_apps
        ;;
    restart)
        restart_apps
        ;;
    status)
        pm2 status
        ;;
    logs)
        pm2 logs
        ;;
    delete)
        delete_apps
        ;;
    setup)
        setup_startup
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo "❌ Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
