{"id": "geoportal-env", "name": "GeoPortal Environment", "values": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "default", "enabled": true}, {"key": "serverUrl", "value": "http://your-server-ip:3000", "type": "default", "enabled": false}, {"key": "uploadId", "value": "", "type": "any", "enabled": true}, {"key": "jobId", "value": "", "type": "any", "enabled": true}, {"key": "userId", "value": "test-user-123", "type": "default", "enabled": true}], "_postman_variable_scope": "environment"}