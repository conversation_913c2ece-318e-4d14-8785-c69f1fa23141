require('dotenv').config();

const config = {
  // Server Configuration
  server: {
    port: process.env.PORT || 3000,
    nodeEnv: process.env.NODE_ENV || 'development'
  },

  // Redis Configuration
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD || undefined,
    maxRetriesPerRequest: 3,
    retryDelayOnFailover: 100,
    enableReadyCheck: false,
    maxRetriesPerRequest: null
  },

  // File Upload Configuration
  upload: {
    chunkSize: parseInt(process.env.CHUNK_SIZE) || 10 * 1024 * 1024, // 10MB
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 100 * 1024 * 1024 * 1024, // 100GB
    uploadTimeout: parseInt(process.env.UPLOAD_TIMEOUT) || 5 * 60 * 1000, // 5 minutes
    allowedExtensions: ['.zip', '.rar', '.7z', '.tar', '.gz', '.tar.gz']
  },

  // Directory Paths
  paths: {
    tempUploadDir: process.env.TEMP_UPLOAD_DIR || './tmp/uploads_bigfiles',
    tempExtractDir: process.env.TEMP_EXTRACT_DIR || './tmp/extracted',
    finalDataDir: process.env.FINAL_DATA_DIR || './data/final',
    logsDir: './logs'
  },

  // Job Queue Configuration
  queue: {
    name: process.env.QUEUE_NAME || 'file-processing-queue',
    maxConcurrentJobs: parseInt(process.env.MAX_CONCURRENT_JOBS) || 3,
    jobTimeout: parseInt(process.env.JOB_TIMEOUT) || 30 * 60 * 1000, // 30 minutes
    removeOnComplete: 50,
    removeOnFail: 100
  },

  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || './logs/app.log'
  }
};

module.exports = config;
