{"info": {"name": "GeoPortal Workflow Server", "description": "API collection for GeoPortal File Upload System", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "uploadId", "value": "", "type": "string"}, {"key": "jobId", "value": "", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}, "response": []}, {"name": "Initialize Upload", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data.uploadId) {", "        pm.collectionVariables.set('uploadId', response.data.uploadId);", "        console.log('Upload ID saved:', response.data.uploadId);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"filename\": \"test-data.zip\",\n  \"totalChunks\": 5,\n  \"totalSize\": 52428800,\n  \"userId\": \"test-user-123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/upload/init-upload", "host": ["{{baseUrl}}"], "path": ["api", "upload", "init-upload"]}}, "response": []}, {"name": "Get Upload Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/upload/{{uploadId}}/status", "host": ["{{baseUrl}}"], "path": ["api", "upload", "{{uploadId}}", "status"]}}, "response": []}, {"name": "Upload Chunk", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data.jobId) {", "        pm.collectionVariables.set('jobId', response.data.jobId);", "        console.log('Job ID saved:', response.data.jobId);", "    }", "}"]}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "uploadId", "value": "{{uploadId}}", "type": "text"}, {"key": "chunkIndex", "value": "0", "type": "text"}, {"key": "chunk", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/api/upload/upload-chunk", "host": ["{{baseUrl}}"], "path": ["api", "upload", "upload-chunk"]}}, "response": []}, {"name": "Get Jobs List", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/jobs?limit=10&offset=0", "host": ["{{baseUrl}}"], "path": ["api", "jobs"], "query": [{"key": "limit", "value": "10"}, {"key": "offset", "value": "0"}, {"key": "status", "value": "completed,failed", "disabled": true}]}}, "response": []}, {"name": "Get Job Stats", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/jobs/stats", "host": ["{{baseUrl}}"], "path": ["api", "jobs", "stats"]}}, "response": []}, {"name": "Get Job Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/jobs/{{jobId}}/status", "host": ["{{baseUrl}}"], "path": ["api", "jobs", "{{jobId}}", "status"]}}, "response": []}, {"name": "Cancel Job", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{baseUrl}}/api/jobs/{{jobId}}/cancel", "host": ["{{baseUrl}}"], "path": ["api", "jobs", "{{jobId}}", "cancel"]}}, "response": []}, {"name": "Retry Job", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{baseUrl}}/api/jobs/{{jobId}}/retry", "host": ["{{baseUrl}}"], "path": ["api", "jobs", "{{jobId}}", "retry"]}}, "response": []}]}