const fs = require('fs-extra');
const path = require('path');
const config = require('../src/config');
const logger = require('../src/utils/logger');

/**
 * Script dọn dẹp hệ thống
 */
class SystemCleanup {
  constructor() {
    this.cleanedFiles = 0;
    this.cleanedSize = 0;
    this.errors = [];
  }

  async cleanAll(options = {}) {
    const {
      cleanTemp = true,
      cleanLogs = false,
      cleanFinal = false,
      olderThanDays = 7,
      dryRun = false
    } = options;

    console.log('🧹 Starting system cleanup...');
    console.log(`Options: temp=${cleanTemp}, logs=${cleanLogs}, final=${cleanFinal}, olderThan=${olderThanDays}days, dryRun=${dryRun}\n`);

    if (cleanTemp) {
      await this.cleanTempFiles(olderThanDays, dryRun);
    }

    if (cleanLogs) {
      await this.cleanLogFiles(olderThanDays, dryRun);
    }

    if (cleanFinal) {
      await this.cleanFinalFiles(olderThanDays, dryRun);
    }

    this.printResults(dryRun);
  }

  async cleanTempFiles(olderThanDays, dryRun) {
    console.log('🗂️  Cleaning temporary files...');
    
    const tempDirs = [
      config.paths.tempUploadDir,
      config.paths.tempExtractDir
    ];

    for (const tempDir of tempDirs) {
      await this.cleanDirectory(tempDir, olderThanDays, dryRun);
    }
  }

  async cleanLogFiles(olderThanDays, dryRun) {
    console.log('📝 Cleaning log files...');
    
    await this.cleanDirectory(config.paths.logsDir, olderThanDays, dryRun, ['.log']);
  }

  async cleanFinalFiles(olderThanDays, dryRun) {
    console.log('📁 Cleaning final files...');
    
    await this.cleanDirectory(config.paths.finalDataDir, olderThanDays, dryRun);
  }

  async cleanDirectory(dirPath, olderThanDays, dryRun, extensions = null) {
    try {
      if (!await fs.pathExists(dirPath)) {
        console.log(`  ⚠️  Directory not found: ${dirPath}`);
        return;
      }

      const items = await fs.readdir(dirPath);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      for (const item of items) {
        const itemPath = path.join(dirPath, item);
        
        try {
          const stats = await fs.stat(itemPath);
          
          // Skip if not old enough
          if (stats.mtime > cutoffDate) {
            continue;
          }

          // Skip if extension filter doesn't match
          if (extensions && !extensions.some(ext => item.endsWith(ext))) {
            continue;
          }

          const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
          
          if (dryRun) {
            console.log(`  [DRY RUN] Would delete: ${itemPath} (${sizeInMB} MB)`);
          } else {
            await fs.remove(itemPath);
            console.log(`  ✅ Deleted: ${itemPath} (${sizeInMB} MB)`);
          }

          this.cleanedFiles++;
          this.cleanedSize += stats.size;
          
        } catch (error) {
          this.errors.push(`Error processing ${itemPath}: ${error.message}`);
          console.log(`  ❌ Error: ${itemPath} - ${error.message}`);
        }
      }
    } catch (error) {
      this.errors.push(`Error accessing directory ${dirPath}: ${error.message}`);
      console.log(`  ❌ Error accessing directory: ${dirPath}`);
    }
  }

  formatSize(bytes) {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  printResults(dryRun) {
    console.log('\n' + '='.repeat(50));
    console.log('🧹 CLEANUP RESULTS');
    console.log('='.repeat(50));

    if (dryRun) {
      console.log('📋 DRY RUN - No files were actually deleted');
    }

    console.log(`📊 Files processed: ${this.cleanedFiles}`);
    console.log(`💾 Space ${dryRun ? 'would be' : ''} freed: ${this.formatSize(this.cleanedSize)}`);

    if (this.errors.length > 0) {
      console.log(`\n❌ Errors encountered: ${this.errors.length}`);
      this.errors.forEach(error => console.log(`  - ${error}`));
    } else {
      console.log('\n✅ Cleanup completed successfully!');
    }

    console.log('='.repeat(50));
  }
}

// Command line interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {};

  // Parse command line arguments
  args.forEach(arg => {
    if (arg === '--dry-run') options.dryRun = true;
    if (arg === '--clean-logs') options.cleanLogs = true;
    if (arg === '--clean-final') options.cleanFinal = true;
    if (arg.startsWith('--days=')) options.olderThanDays = parseInt(arg.split('=')[1]);
  });

  const cleanup = new SystemCleanup();
  cleanup.cleanAll(options).catch(console.error);
}

module.exports = SystemCleanup;
