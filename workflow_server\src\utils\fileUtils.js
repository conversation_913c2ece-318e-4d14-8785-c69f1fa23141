const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const config = require('../config');

/**
 * T<PERSON><PERSON> thư mục nếu chưa tồn tại
 */
const ensureDirectoryExists = async (dirPath) => {
  try {
    await fs.ensureDir(dirPath);
    return true;
  } catch (error) {
    console.error(`Error creating directory ${dirPath}:`, error);
    return false;
  }
};

/**
 * Tạo upload ID duy nhất
 */
const generateUploadId = () => {
  return uuidv4();
};

/**
 * Tạo job ID duy nhất
 */
const generateJobId = () => {
  return uuidv4();
};

/**
 * Lấy đường dẫn thư mục upload tạm
 */
const getUploadTempDir = (uploadId) => {
  return path.join(config.paths.tempUploadDir, uploadId);
};

/**
 * <PERSON><PERSON><PERSON> đường dẫn thư mục giải nén tạm
 */
const getExtractTempDir = (jobId) => {
  return path.join(config.paths.tempExtractDir, jobId);
};

/**
 * Lấy đường dẫn thư mục đích cuối cùng
 */
const getFinalDir = (userId, timestamp) => {
  const dirName = `${userId}_${timestamp}`;
  return path.join(config.paths.finalDataDir, dirName);
};

/**
 * Lấy đường dẫn file chunk
 */
const getChunkPath = (uploadId, chunkIndex) => {
  const uploadDir = getUploadTempDir(uploadId);
  return path.join(uploadDir, `chunk_${chunkIndex}`);
};

/**
 * Lấy đường dẫn file meta
 */
const getMetaPath = (uploadId) => {
  const uploadDir = getUploadTempDir(uploadId);
  return path.join(uploadDir, 'meta.json');
};

/**
 * Lấy đường dẫn file hoàn chỉnh sau khi ghép
 */
const getCompleteFilePath = (uploadId, filename) => {
  const uploadDir = getUploadTempDir(uploadId);
  return path.join(uploadDir, filename);
};

/**
 * Kiểm tra extension file có được phép không
 */
const isAllowedExtension = (filename) => {
  const ext = path.extname(filename).toLowerCase();
  return config.upload.allowedExtensions.includes(ext);
};

/**
 * Lấy kích thước file
 */
const getFileSize = async (filePath) => {
  try {
    const stats = await fs.stat(filePath);
    return stats.size;
  } catch (error) {
    return 0;
  }
};

/**
 * Xóa thư mục và tất cả nội dung bên trong
 */
const removeDirectory = async (dirPath) => {
  try {
    await fs.remove(dirPath);
    console.log(`Removed directory: ${dirPath}`);
    return true;
  } catch (error) {
    console.error(`Error removing directory ${dirPath}:`, error);
    return false;
  }
};

/**
 * Tạo timestamp cho tên thư mục
 */
const generateTimestamp = () => {
  return new Date().toISOString().replace(/[:.]/g, '-');
};

/**
 * Kiểm tra file có tồn tại không
 */
const fileExists = async (filePath) => {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
};

module.exports = {
  ensureDirectoryExists,
  generateUploadId,
  generateJobId,
  getUploadTempDir,
  getExtractTempDir,
  getFinalDir,
  getChunkPath,
  getMetaPath,
  getCompleteFilePath,
  isAllowedExtension,
  getFileSize,
  removeDirectory,
  generateTimestamp,
  fileExists
};
