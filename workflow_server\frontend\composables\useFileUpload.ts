import { ref, computed, reactive } from 'vue'
import type {
  Job,
  UploadSession,
  UploadProgress,
  ApiResponse,
  UploadInitRequest,
  JobStats,
  UploadInfo
} from '../types'

export function useFileUpload(baseUrl: string = 'http://localhost:3000/api') {
  // State
  const isUploading = ref(false)
  const error = ref<string | null>(null)
  const currentJob = ref<Job | null>(null)
  const jobs = ref<Job[]>([])
  const uploadSession = ref<UploadSession | null>(null)
  
  const uploadProgress = reactive<UploadProgress>({
    show: false,
    status: 'Preparing...',
    percentage: 0,
    chunksUploaded: 0,
    speed: '0 MB/s'
  })

  // Configuration
  const CHUNK_SIZE = 10 * 1024 * 1024 // 10MB
  const POLL_INTERVAL = 2000 // 2 seconds

  // Polling interval
  let jobPollingInterval: number | null = null

  // Computed
  const hasError = computed(() => error.value !== null)
  const isJobRunning = computed(() => currentJob.value?.status === 'running')

  // Utility functions
  const clearError = () => {
    error.value = null
  }

  const setError = (message: string) => {
    error.value = message
    console.error('Upload Error:', message)
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // API methods
  const apiRequest = async <T = any>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> => {
    try {
      const response = await fetch(`${baseUrl}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        ...options
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return await response.json()
    } catch (err) {
      console.error('API Request failed:', err)
      throw err
    }
  }

  const initializeUpload = async (file: File, userId: string = 'vue-client'): Promise<UploadSession> => {
    const totalChunks = Math.ceil(file.size / CHUNK_SIZE)
    
    const payload: UploadInitRequest = {
      filename: file.name,
      totalChunks,
      totalSize: file.size,
      userId
    }

    const response = await apiRequest<UploadSession>('/upload/init-upload', {
      method: 'POST',
      body: JSON.stringify(payload)
    })

    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to initialize upload')
    }

    return response.data
  }

  const uploadChunk = async (chunkIndex: number, chunk: Blob): Promise<any> => {
    if (!uploadSession.value) {
      throw new Error('No upload session')
    }

    const formData = new FormData()
    formData.append('uploadId', uploadSession.value.uploadId)
    formData.append('chunkIndex', chunkIndex.toString())
    formData.append('chunk', chunk)

    const response = await fetch(`${baseUrl}/upload/upload-chunk`, {
      method: 'POST',
      body: formData
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const result = await response.json()
    if (!result.success) {
      throw new Error(result.error || 'Failed to upload chunk')
    }

    return result.data
  }

  const getUploadStatus = async (uploadId: string): Promise<UploadInfo | null> => {
    try {
      const response = await apiRequest<UploadInfo>(`/upload/${uploadId}/status`)
      return response.success ? response.data || null : null
    } catch (err) {
      console.error('Failed to get upload status:', err)
      return null
    }
  }

  const uploadFile = async (file: File, userId: string = 'vue-client'): Promise<void> => {
    try {
      isUploading.value = true
      uploadProgress.show = true
      uploadProgress.status = 'Initializing upload...'
      uploadProgress.percentage = 0
      uploadProgress.chunksUploaded = 0
      clearError()

      // Initialize upload session
      uploadSession.value = await initializeUpload(file, userId)
      
      const totalChunks = Math.ceil(file.size / CHUNK_SIZE)
      uploadProgress.status = 'Uploading chunks...'
      
      const startTime = Date.now()
      let uploadedBytes = 0

      // Upload chunks
      for (let i = 0; i < totalChunks; i++) {
        const start = i * CHUNK_SIZE
        const end = Math.min(start + CHUNK_SIZE, file.size)
        const chunk = file.slice(start, end)

        const result = await uploadChunk(i, chunk)
        
        uploadedBytes += chunk.size
        uploadProgress.chunksUploaded = i + 1
        uploadProgress.percentage = Math.round((uploadProgress.chunksUploaded / totalChunks) * 100)
        
        // Calculate upload speed
        const elapsed = (Date.now() - startTime) / 1000
        const speed = uploadedBytes / elapsed
        uploadProgress.speed = formatFileSize(speed) + '/s'
        
        uploadProgress.status = `Uploading chunk ${i + 1} of ${totalChunks}...`

        // If upload completed and job created
        if (result.isComplete && result.jobId) {
          currentJob.value = {
            id: result.jobId,
            filename: file.name,
            status: 'pending',
            createdAt: new Date().toISOString()
          }
          startJobPolling()
          break
        }
      }

      uploadProgress.status = 'Upload completed!'
      
      // Load jobs list
      await loadJobs()

    } catch (err) {
      const message = err instanceof Error ? err.message : 'Upload failed'
      setError(message)
      throw err
    } finally {
      isUploading.value = false
    }
  }

  // Job management
  const loadJobs = async (limit: number = 10, offset: number = 0): Promise<void> => {
    try {
      const response = await apiRequest<{ jobs: Job[], total: number }>(`/jobs?limit=${limit}&offset=${offset}`)
      if (response.success && response.data) {
        jobs.value = response.data.jobs || []
      }
    } catch (err) {
      console.error('Failed to load jobs:', err)
    }
  }

  const getJobStatus = async (jobId: string): Promise<Job | null> => {
    try {
      const response = await apiRequest<Job>(`/jobs/${jobId}/status`)
      return response.success && response.data ? response.data : null
    } catch (err) {
      console.error('Failed to get job status:', err)
      return null
    }
  }

  const getJobStats = async (): Promise<JobStats | null> => {
    try {
      const response = await apiRequest<JobStats>('/jobs/stats')
      return response.success && response.data ? response.data : null
    } catch (err) {
      console.error('Failed to get job stats:', err)
      return null
    }
  }

  const cancelJob = async (jobId: string): Promise<boolean> => {
    try {
      const response = await apiRequest(`/jobs/${jobId}/cancel`, { method: 'POST' })
      return response.success
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to cancel job'
      setError(message)
      return false
    }
  }

  const retryJob = async (jobId: string): Promise<boolean> => {
    try {
      const response = await apiRequest(`/jobs/${jobId}/retry`, { method: 'POST' })
      if (response.success) {
        startJobPolling()
        return true
      }
      return false
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to retry job'
      setError(message)
      return false
    }
  }

  const refreshJobStatus = async (): Promise<void> => {
    if (!currentJob.value) return
    
    const status = await getJobStatus(currentJob.value.id)
    if (status) {
      currentJob.value = { ...currentJob.value, ...status }
    }
  }

  const selectJob = async (job: Job): Promise<void> => {
    currentJob.value = job
    const status = await getJobStatus(job.id)
    if (status) {
      currentJob.value = { ...currentJob.value, ...status }
    }
    
    if (job.status === 'running') {
      startJobPolling()
    }
  }

  // Polling
  const startJobPolling = (): void => {
    if (jobPollingInterval) {
      clearInterval(jobPollingInterval)
    }
    
    jobPollingInterval = setInterval(async () => {
      if (!currentJob.value) {
        stopJobPolling()
        return
      }
      
      await refreshJobStatus()
      
      // Stop polling if job is completed or failed
      if (currentJob.value.status === 'completed' || currentJob.value.status === 'failed') {
        stopJobPolling()
        await loadJobs() // Refresh jobs list
      }
    }, POLL_INTERVAL)
  }

  const stopJobPolling = (): void => {
    if (jobPollingInterval) {
      clearInterval(jobPollingInterval)
      jobPollingInterval = null
    }
  }

  // Health check
  const checkHealth = async (): Promise<boolean> => {
    try {
      const response = await fetch(`${baseUrl.replace('/api', '')}/health`)
      return response.ok
    } catch (err) {
      return false
    }
  }

  // Cleanup
  const cleanup = (): void => {
    stopJobPolling()
    uploadSession.value = null
    currentJob.value = null
    uploadProgress.show = false
    clearError()
  }

  return {
    // State
    isUploading: readonly(isUploading),
    error: readonly(error),
    currentJob: readonly(currentJob),
    jobs: readonly(jobs),
    uploadSession: readonly(uploadSession),
    uploadProgress: readonly(uploadProgress),
    
    // Computed
    hasError,
    isJobRunning,
    
    // Methods
    uploadFile,
    loadJobs,
    getJobStatus,
    getJobStats,
    cancelJob,
    retryJob,
    refreshJobStatus,
    selectJob,
    getUploadStatus,
    checkHealth,
    clearError,
    cleanup,
    
    // Polling
    startJobPolling,
    stopJobPolling,
    
    // Utils
    formatFileSize
  }
}
