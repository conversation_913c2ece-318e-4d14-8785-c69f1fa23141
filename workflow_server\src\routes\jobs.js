const express = require('express');
const {
  getJobStatus,
  getJobs,
  cancelJob,
  retryJob,
  getJobStats
} = require('../controllers/jobController');

const router = express.Router();

/**
 * @route   GET /api/jobs/stats
 * @desc    Lấy thống kê jobs
 * @access  Public
 */
router.get('/stats', getJobStats);

/**
 * @route   GET /api/jobs
 * @desc    Lấy danh sách jobs với phân trang và filter
 * @access  Public
 * @query   { status?, limit?, offset?, userId? }
 */
router.get('/', getJobs);

/**
 * @route   GET /api/jobs/:id/status
 * @desc    Lấy trạng thái job theo ID
 * @access  Public
 */
router.get('/:id/status', getJobStatus);

/**
 * @route   POST /api/jobs/:id/cancel
 * @desc    Hủy job
 * @access  Public
 */
router.post('/:id/cancel', cancelJob);

/**
 * @route   POST /api/jobs/:id/retry
 * @desc    Retry failed job
 * @access  Public
 */
router.post('/:id/retry', retryJob);

module.exports = router;
